#!/bin/bash
# Install and setup Ollama for running LLMs locally
# Ollama is optimized for local inference and handles quantization automatically

echo "🚀 Installing Ollama for local LLM inference"
echo "============================================"

# Install Ollama
echo "📥 Downloading and installing Ollama..."
curl -fsSL https://ollama.ai/install.sh | sh

# Check if installation was successful
if command -v ollama &> /dev/null; then
    echo "✅ Ollama installed successfully!"
    echo "📍 Version: $(ollama --version)"
else
    echo "❌ Ollama installation failed"
    exit 1
fi

echo ""
echo "🎯 Recommended models for your 4GB GPU:"
echo ""

echo "1. 📦 Llama 3.2 3B (Fits comfortably in 4GB)"
echo "   ollama pull llama3.2:3b"
echo ""

echo "2. 📦 Qwen2.5 7B (With quantization)"
echo "   ollama pull qwen2.5:7b-instruct-q4_0"
echo ""

echo "3. 📦 Phi-3.5 Mini (Microsoft, very efficient)"
echo "   ollama pull phi3.5:3.8b"
echo ""

echo "4. 📦 Gemma 2 2B (Google, lightweight)"
echo "   ollama pull gemma2:2b"
echo ""

echo "💡 Usage examples:"
echo "   # Start Ollama service"
echo "   ollama serve"
echo ""
echo "   # Pull and run a model"
echo "   ollama pull llama3.2:3b"
echo "   ollama run llama3.2:3b"
echo ""
echo "   # API usage"
echo "   curl http://localhost:11434/api/generate -d '{\"model\":\"llama3.2:3b\",\"prompt\":\"Hello!\"}'"
echo ""

echo "🔧 To start using Ollama:"
echo "1. Run: ollama serve (in one terminal)"
echo "2. Run: ollama pull llama3.2:3b (to download a model)"
echo "3. Run: ollama run llama3.2:3b (to start chatting)"
echo ""

echo "✅ Ollama setup complete!"
