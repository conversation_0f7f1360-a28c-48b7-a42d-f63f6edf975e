#!/usr/bin/env python3
"""
Test script to load only the Hunyuan tokenizer and analyze model requirements
This helps understand compatibility without downloading the full 26GB+ model
"""

import torch
import json
import os
from pathlib import Path
from transformers import AutoTokenizer
import sys

def analyze_model_config(cache_dir="./hunyuan_cache"):
    """Analyze the model configuration to understand requirements"""
    
    print("=== Model Configuration Analysis ===")
    
    # Find the config file
    config_path = None
    for root, dirs, files in os.walk(cache_dir):
        if "config.json" in files:
            config_path = os.path.join(root, "config.json")
            break
    
    if not config_path:
        print("❌ config.json not found in cache directory")
        return None
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        print(f"✅ Model configuration loaded from: {config_path}")
        print(f"📊 Model details:")
        
        # Key parameters
        important_params = [
            'model_type', 'architectures', 'hidden_size', 'num_hidden_layers',
            'num_attention_heads', 'vocab_size', 'max_position_embeddings',
            'torch_dtype', 'num_key_value_heads'
        ]
        
        for param in important_params:
            if param in config:
                print(f"   • {param}: {config[param]}")
        
        # Calculate approximate memory requirements
        if 'hidden_size' in config and 'num_hidden_layers' in config:
            hidden_size = config['hidden_size']
            num_layers = config['num_hidden_layers']
            vocab_size = config.get('vocab_size', 50000)
            
            # Rough calculation (this is simplified)
            # Each parameter is typically 2 bytes (float16) or 4 bytes (float32)
            params_per_layer = hidden_size * hidden_size * 4  # Simplified estimate
            total_params = params_per_layer * num_layers + vocab_size * hidden_size
            
            memory_fp16_gb = (total_params * 2) / (1024**3)
            memory_fp32_gb = (total_params * 4) / (1024**3)
            
            print(f"\n💾 Estimated memory requirements:")
            print(f"   • Parameters: ~{total_params/1e9:.1f}B")
            print(f"   • FP16 memory: ~{memory_fp16_gb:.1f} GB")
            print(f"   • FP32 memory: ~{memory_fp32_gb:.1f} GB")
            print(f"   • Your GPU VRAM: 4 GB")
            
            if memory_fp16_gb > 4:
                print(f"   ⚠️  Model too large for your GPU!")
                print(f"   💡 Suggestions:")
                print(f"      - Use CPU inference (very slow)")
                print(f"      - Use quantization (8-bit/4-bit)")
                print(f"      - Use model sharding across CPU+GPU")
                print(f"      - Try smaller models")
        
        return config
        
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return None

def test_tokenizer_loading(cache_dir="./hunyuan_cache"):
    """Test loading the tokenizer"""
    
    print("\n=== Tokenizer Loading Test ===")
    
    model_name = "tencent/Hunyuan-A13B-Instruct"
    
    try:
        print(f"🔄 Loading tokenizer from cache...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            cache_dir=cache_dir,
            trust_remote_code=True,
            local_files_only=True  # Only use cached files
        )
        
        print(f"✅ Tokenizer loaded successfully!")
        print(f"📊 Tokenizer info:")
        print(f"   • Vocab size: {tokenizer.vocab_size}")
        print(f"   • Model max length: {tokenizer.model_max_length}")
        print(f"   • Tokenizer class: {type(tokenizer).__name__}")
        
        # Test tokenization
        test_text = "Hello, how are you today? 你好，今天怎么样？"
        print(f"\n🧪 Testing tokenization:")
        print(f"   Input: {test_text}")
        
        tokens = tokenizer.encode(test_text)
        decoded = tokenizer.decode(tokens)
        
        print(f"   Tokens: {tokens[:10]}... (showing first 10)")
        print(f"   Token count: {len(tokens)}")
        print(f"   Decoded: {decoded}")
        
        return tokenizer
        
    except Exception as e:
        print(f"❌ Error loading tokenizer: {e}")
        return None

def check_system_compatibility():
    """Check system compatibility for running the model"""
    
    print("\n=== System Compatibility Check ===")
    
    # Check PyTorch and CUDA
    print(f"🔧 Software versions:")
    print(f"   • PyTorch: {torch.__version__}")
    print(f"   • CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
        compute_capability = torch.cuda.get_device_capability(0)
        
        print(f"   • GPU: {gpu_name}")
        print(f"   • VRAM: {gpu_memory:.1f} GB")
        print(f"   • Compute capability: {compute_capability[0]}.{compute_capability[1]}")
        
        # Check compatibility
        print(f"\n🔍 Compatibility assessment:")
        
        if gpu_memory < 8:
            print(f"   ⚠️  Low VRAM: {gpu_memory:.1f} GB < 8 GB recommended")
            print(f"   💡 Consider: CPU inference, quantization, or smaller models")
        
        if compute_capability[0] < 7:
            print(f"   ⚠️  Old GPU architecture: {compute_capability[0]}.{compute_capability[1]}")
            print(f"   💡 May have limited optimization support")
        
        # Check available RAM
        try:
            import psutil
            ram_gb = psutil.virtual_memory().total / (1024**3)
            available_ram_gb = psutil.virtual_memory().available / (1024**3)
            print(f"   • System RAM: {ram_gb:.1f} GB (available: {available_ram_gb:.1f} GB)")
            
            if available_ram_gb < 16:
                print(f"   ⚠️  Limited RAM for large model CPU fallback")
        except ImportError:
            print(f"   • System RAM: Unable to check (install psutil)")
    
    else:
        print(f"   ⚠️  No CUDA GPU detected - CPU only mode")

def suggest_alternatives():
    """Suggest alternative approaches"""
    
    print("\n=== Alternative Approaches ===")
    
    print("🎯 Recommended options for your system:")
    print()
    
    print("1. 🔧 Use smaller models:")
    print("   • Qwen2.5-7B-Instruct (fits in 4GB with quantization)")
    print("   • Llama-3.2-3B-Instruct")
    print("   • Phi-3.5-mini-instruct")
    print()
    
    print("2. 🗜️ Use quantized versions:")
    print("   • 8-bit quantization (bitsandbytes)")
    print("   • 4-bit quantization (QLoRA)")
    print("   • GGUF format with llama.cpp")
    print()
    
    print("3. ☁️ Cloud solutions:")
    print("   • Google Colab (free GPU)")
    print("   • Hugging Face Spaces")
    print("   • RunPod, Vast.ai (paid GPU rental)")
    print()
    
    print("4. 🔄 Alternative inference engines:")
    print("   • Ollama (optimized for local inference)")
    print("   • llama.cpp (CPU optimized)")
    print("   • Text Generation WebUI")

def main():
    """Main function"""
    
    print("🚀 Hunyuan-A13B-Instruct Compatibility Analysis")
    print("=" * 60)
    
    cache_dir = "./hunyuan_cache"
    
    # Check if we have the downloaded files
    if not os.path.exists(cache_dir):
        print(f"❌ Cache directory not found: {cache_dir}")
        print(f"💡 Run: python3 download_hunyuan_files.py --no-weights")
        return
    
    # Analyze configuration
    config = analyze_model_config(cache_dir)
    
    # Test tokenizer
    tokenizer = test_tokenizer_loading(cache_dir)
    
    # Check system compatibility
    check_system_compatibility()
    
    # Suggest alternatives
    suggest_alternatives()
    
    print("\n" + "=" * 60)
    print("📋 Summary:")
    
    if config and tokenizer:
        print("✅ Model files downloaded and tokenizer working")
        print("⚠️  Full model likely too large for your 4GB GPU")
        print("💡 Consider alternatives listed above")
    else:
        print("❌ Issues detected with model files or tokenizer")

if __name__ == "__main__":
    main()
