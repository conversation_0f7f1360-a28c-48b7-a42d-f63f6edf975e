#!/usr/bin/env python3
"""
Test script to run Hunyuan-A13B-Instruct using Hugging Face Transformers
with optimizations for limited VRAM (4GB GTX 1650 Ti)
Downloads model files one by one for better progress tracking and control
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from huggingface_hub import hf_hub_download, snapshot_download
import gc
import os
from pathlib import Path

def download_model_files_individually(model_name, cache_dir=None):
    """Download model files one by one with progress tracking"""
    print(f"\n=== Downloading {model_name} files individually ===")

    # Essential files to download first
    essential_files = [
        "config.json",
        "tokenizer_config.json",
        "tokenizer.json",
        "special_tokens_map.json",
        "generation_config.json"
    ]

    # Custom model files
    custom_files = [
        "configuration_hunyuan.py",
        "modeling_hunyuan.py",
        "tokenization_hy.py",
        "hunyuan.py",
        "hy.tiktoken"
    ]

    # Model weight files (these are large)
    weight_files = [
        "pytorch_model.bin",
        "model.safetensors",
        "pytorch_model-00001-of-00006.bin",
        "pytorch_model-00002-of-00006.bin",
        "pytorch_model-00003-of-00006.bin",
        "pytorch_model-00004-of-00006.bin",
        "pytorch_model-00005-of-00006.bin",
        "pytorch_model-00006-of-00006.bin",
        "pytorch_model.bin.index.json"
    ]

    all_files = essential_files + custom_files + weight_files
    downloaded_files = []

    for file_name in all_files:
        try:
            print(f"\nDownloading: {file_name}")
            file_path = hf_hub_download(
                repo_id=model_name,
                filename=file_name,
                cache_dir=cache_dir,
                resume_download=True,
                local_files_only=False
            )
            downloaded_files.append(file_path)
            print(f"✓ Downloaded: {file_name}")

            # Show file size
            if os.path.exists(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  Size: {size_mb:.1f} MB")

        except Exception as e:
            print(f"⚠ Could not download {file_name}: {e}")
            # Continue with other files
            continue

    print(f"\n✓ Downloaded {len(downloaded_files)} files successfully")
    return downloaded_files

def test_hunyuan_model():
    print("=== System Information ===")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    model_name = "tencent/Hunyuan-A13B-Instruct"
    
    try:
        print(f"\n=== Loading Model: {model_name} ===")

        # First download files individually
        cache_dir = "./model_cache"
        downloaded_files = download_model_files_individually(model_name, cache_dir)

        if not downloaded_files:
            print("No files downloaded successfully. Trying direct download...")

        # Load tokenizer
        print("\nLoading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(
            model_name,
            trust_remote_code=True,
            cache_dir=cache_dir
        )

        # Load model with optimizations for limited VRAM
        print("Loading model with optimizations...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype=torch.float16,  # Use half precision
            device_map="auto",          # Automatic device mapping
            low_cpu_mem_usage=True,     # Reduce CPU memory usage
            load_in_8bit=True,          # 8-bit quantization to reduce VRAM
            cache_dir=cache_dir
        )
        
        print(f"Model loaded successfully!")
        print(f"Model device: {next(model.parameters()).device}")
        print(f"Model dtype: {next(model.parameters()).dtype}")
        
        # Test inference
        print("\n=== Testing Inference ===")
        prompt = "Hello, how are you today?"
        
        inputs = tokenizer(prompt, return_tensors="pt")
        if torch.cuda.is_available():
            inputs = {k: v.cuda() for k, v in inputs.items()}
        
        print(f"Input prompt: {prompt}")
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=50,
                do_sample=True,
                temperature=0.7,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"Generated response: {response}")
        
        return True
        
    except torch.cuda.OutOfMemoryError as e:
        print(f"CUDA Out of Memory Error: {e}")
        print("Try running with CPU-only mode or smaller model")
        return False
        
    except Exception as e:
        print(f"Error loading model: {e}")
        return False
    
    finally:
        # Clean up memory
        if 'model' in locals():
            del model
        if 'tokenizer' in locals():
            del tokenizer
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

def test_cpu_fallback():
    """Test with CPU-only mode if GPU fails"""
    print("\n=== Testing CPU-only mode ===")
    model_name = "tencent/Hunyuan-A13B-Instruct"
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(
            model_name, 
            trust_remote_code=True
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            trust_remote_code=True,
            torch_dtype=torch.float32,
            device_map="cpu",
            low_cpu_mem_usage=True,
        )
        
        print("Model loaded on CPU successfully!")
        
        # Quick test
        prompt = "Hello"
        inputs = tokenizer(prompt, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=10,
                do_sample=False,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"CPU inference successful: {response}")
        return True
        
    except Exception as e:
        print(f"CPU fallback failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing Hunyuan-A13B-Instruct compatibility...")
    
    # Try GPU first
    gpu_success = test_hunyuan_model()
    
    if not gpu_success:
        print("\nGPU failed, trying CPU fallback...")
        cpu_success = test_cpu_fallback()
        
        if not cpu_success:
            print("\nBoth GPU and CPU failed. Consider:")
            print("1. Using a smaller model")
            print("2. Using cloud GPU services")
            print("3. Using quantized versions")
