#!/usr/bin/env python3
"""
Download Hunyuan-A13B-Instruct model files one by one
This allows better control over the download process and progress tracking
"""

import os
import sys
from pathlib import Path
from huggingface_hub import hf_hub_download, list_repo_files
import time

def download_file_with_progress(repo_id, filename, cache_dir=None, max_retries=3):
    """Download a single file with retry logic and progress info"""
    
    for attempt in range(max_retries):
        try:
            print(f"\n📥 Downloading: {filename} (attempt {attempt + 1}/{max_retries})")
            start_time = time.time()
            
            file_path = hf_hub_download(
                repo_id=repo_id,
                filename=filename,
                cache_dir=cache_dir,
                resume_download=True,
                local_files_only=False
            )
            
            end_time = time.time()
            download_time = end_time - start_time
            
            if os.path.exists(file_path):
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                speed_mbps = size_mb / download_time if download_time > 0 else 0
                print(f"✅ Success: {filename}")
                print(f"   📁 Path: {file_path}")
                print(f"   📊 Size: {size_mb:.1f} MB")
                print(f"   ⏱️  Time: {download_time:.1f}s ({speed_mbps:.1f} MB/s)")
                return file_path
            else:
                print(f"❌ File not found after download: {filename}")
                
        except KeyboardInterrupt:
            print(f"\n⚠️  Download interrupted by user")
            sys.exit(1)
            
        except Exception as e:
            print(f"❌ Error downloading {filename}: {e}")
            if attempt < max_retries - 1:
                print(f"   🔄 Retrying in 5 seconds...")
                time.sleep(5)
            else:
                print(f"   ❌ Failed after {max_retries} attempts")
                return None
    
    return None

def list_all_model_files(repo_id):
    """List all files in the repository"""
    try:
        print(f"📋 Listing all files in {repo_id}...")
        files = list_repo_files(repo_id)
        print(f"Found {len(files)} files:")
        for i, file in enumerate(files, 1):
            print(f"  {i:2d}. {file}")
        return files
    except Exception as e:
        print(f"❌ Error listing files: {e}")
        return []

def download_hunyuan_model(cache_dir="./hunyuan_cache", download_weights=True):
    """Download Hunyuan model files systematically"""
    
    repo_id = "tencent/Hunyuan-A13B-Instruct"
    
    print(f"🚀 Starting download of {repo_id}")
    print(f"📁 Cache directory: {os.path.abspath(cache_dir)}")
    
    # Create cache directory
    Path(cache_dir).mkdir(parents=True, exist_ok=True)
    
    # List all files first
    all_files = list_all_model_files(repo_id)
    
    if not all_files:
        print("❌ Could not list repository files. Exiting.")
        return
    
    # Categorize files by priority
    config_files = [f for f in all_files if f.endswith(('.json', '.py', '.tiktoken'))]
    weight_files = [f for f in all_files if f.endswith(('.bin', '.safetensors'))]
    other_files = [f for f in all_files if f not in config_files and f not in weight_files]
    
    print(f"\n📊 File categories:")
    print(f"   ⚙️  Config files: {len(config_files)}")
    print(f"   🏋️  Weight files: {len(weight_files)}")
    print(f"   📄 Other files: {len(other_files)}")
    
    downloaded_files = []
    failed_files = []
    
    # Download config files first (small and essential)
    print(f"\n🔧 Phase 1: Downloading configuration files...")
    for file in config_files:
        result = download_file_with_progress(repo_id, file, cache_dir)
        if result:
            downloaded_files.append(file)
        else:
            failed_files.append(file)
    
    # Download other small files
    print(f"\n📄 Phase 2: Downloading other files...")
    for file in other_files:
        result = download_file_with_progress(repo_id, file, cache_dir)
        if result:
            downloaded_files.append(file)
        else:
            failed_files.append(file)
    
    # Download weight files (large, optional based on parameter)
    if download_weights:
        print(f"\n🏋️  Phase 3: Downloading model weights...")
        print(f"⚠️  Warning: Weight files are very large (several GB each)")
        
        for i, file in enumerate(weight_files, 1):
            print(f"\n📦 Weight file {i}/{len(weight_files)}: {file}")
            
            # Ask user confirmation for each large file
            response = input(f"Download {file}? (y/n/q to quit): ").lower().strip()
            
            if response == 'q':
                print("🛑 Download stopped by user")
                break
            elif response == 'y':
                result = download_file_with_progress(repo_id, file, cache_dir)
                if result:
                    downloaded_files.append(file)
                else:
                    failed_files.append(file)
            else:
                print(f"⏭️  Skipped: {file}")
    else:
        print(f"\n⏭️  Skipping weight files (download_weights=False)")
    
    # Summary
    print(f"\n📊 Download Summary:")
    print(f"   ✅ Successfully downloaded: {len(downloaded_files)} files")
    print(f"   ❌ Failed downloads: {len(failed_files)} files")
    
    if downloaded_files:
        print(f"\n✅ Downloaded files:")
        for file in downloaded_files:
            print(f"   • {file}")
    
    if failed_files:
        print(f"\n❌ Failed files:")
        for file in failed_files:
            print(f"   • {file}")
    
    return downloaded_files, failed_files

def main():
    """Main function with command line options"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Download Hunyuan-A13B-Instruct model files")
    parser.add_argument("--cache-dir", default="./hunyuan_cache", 
                       help="Directory to store downloaded files")
    parser.add_argument("--no-weights", action="store_true",
                       help="Skip downloading large weight files")
    parser.add_argument("--list-only", action="store_true",
                       help="Only list files, don't download")
    
    args = parser.parse_args()
    
    repo_id = "tencent/Hunyuan-A13B-Instruct"
    
    if args.list_only:
        list_all_model_files(repo_id)
        return
    
    download_weights = not args.no_weights
    download_hunyuan_model(args.cache_dir, download_weights)

if __name__ == "__main__":
    main()
