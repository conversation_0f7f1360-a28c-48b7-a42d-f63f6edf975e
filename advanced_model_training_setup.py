#!/usr/bin/env python3
"""
Advanced Model Training Setup for 4GB GPU
Focus: Fine-tuning Google Gemma-3n-E4B-it for specialized domain
This is the most advanced model compatible with your hardware (2025)
"""

import torch
import os
from transformers import (
    AutoTokenizer, AutoModelForCausalLM, AutoProcessor,
    TrainingArguments, Trainer, DataCollatorForLanguageModeling,
    BitsAndBytesConfig
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset, load_dataset
import json
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedModelTrainer:
    """Advanced training setup for Gemma-3n-E4B-it with LoRA fine-tuning"""
    
    def __init__(self, 
                 model_name="google/gemma-3n-E4B-it",
                 output_dir="./gemma-3n-finetuned",
                 specialization_field="medical"):
        
        self.model_name = model_name
        self.output_dir = output_dir
        self.specialization_field = specialization_field
        
        # Create output directory
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info(f"🚀 Initializing training for {model_name}")
        logger.info(f"🎯 Specialization field: {specialization_field}")
        
    def setup_quantization_config(self):
        """Setup 4-bit quantization for memory efficiency"""
        return BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True,
        )
    
    def setup_lora_config(self):
        """Setup LoRA configuration for efficient fine-tuning"""
        return LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=16,  # Rank
            lora_alpha=32,  # LoRA scaling parameter
            lora_dropout=0.1,
            target_modules=[
                "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj"
            ],
            bias="none",
        )
    
    def load_model_and_tokenizer(self):
        """Load model and tokenizer with optimizations"""
        
        logger.info("📥 Loading tokenizer...")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_name,
            trust_remote_code=True,
            padding_side="right"
        )
        
        # Add pad token if not present
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        logger.info("📥 Loading model with quantization...")
        quantization_config = self.setup_quantization_config()
        
        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name,
            quantization_config=quantization_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16,
            low_cpu_mem_usage=True
        )
        
        # Setup LoRA
        logger.info("🔧 Setting up LoRA...")
        lora_config = self.setup_lora_config()
        self.model = get_peft_model(self.model, lora_config)
        
        # Print trainable parameters
        self.model.print_trainable_parameters()
        
        logger.info("✅ Model and tokenizer loaded successfully!")
    
    def create_specialized_dataset(self):
        """Create or load specialized dataset for the chosen field"""
        
        logger.info(f"📊 Creating {self.specialization_field} dataset...")
        
        # Define specialized datasets by field
        dataset_configs = {
            "medical": {
                "dataset": "medalpaca/medical_meadow_medical_flashcards",
                "text_column": "instruction",
                "description": "Medical knowledge and terminology"
            },
            "legal": {
                "dataset": "pile-of-law/pile-of-law",
                "text_column": "text", 
                "description": "Legal documents and terminology"
            },
            "code": {
                "dataset": "codeparrot/github-code-clean",
                "text_column": "code",
                "description": "Programming and software development"
            },
            "science": {
                "dataset": "scientific_papers",
                "text_column": "abstract",
                "description": "Scientific research and papers"
            },
            "finance": {
                "dataset": "financial_phrasebank",
                "text_column": "sentence",
                "description": "Financial analysis and terminology"
            }
        }
        
        if self.specialization_field not in dataset_configs:
            logger.warning(f"Field '{self.specialization_field}' not predefined. Creating custom dataset...")
            return self.create_custom_dataset()
        
        config = dataset_configs[self.specialization_field]
        
        try:
            # Try to load the dataset
            logger.info(f"Loading {config['dataset']}...")
            dataset = load_dataset(config["dataset"], split="train[:1000]")  # Limit for demo
            
            # Process the dataset
            def process_examples(examples):
                texts = []
                for text in examples[config["text_column"]]:
                    if isinstance(text, str) and len(text.strip()) > 10:
                        # Format as instruction-response for chat model
                        formatted_text = f"<start_of_turn>user\nExplain this {self.specialization_field} concept: {text[:200]}...<end_of_turn>\n<start_of_turn>model\n{text}<end_of_turn>"
                        texts.append(formatted_text)
                return {"text": texts}
            
            dataset = dataset.map(process_examples, batched=True, remove_columns=dataset.column_names)
            
            logger.info(f"✅ Loaded {len(dataset)} examples for {self.specialization_field} training")
            return dataset
            
        except Exception as e:
            logger.error(f"Failed to load dataset: {e}")
            return self.create_custom_dataset()
    
    def create_custom_dataset(self):
        """Create a custom dataset with sample data"""
        
        logger.info("📝 Creating custom sample dataset...")
        
        # Sample data for different fields
        sample_data = {
            "medical": [
                "Hypertension is a condition where blood pressure is consistently elevated above normal levels.",
                "Diabetes mellitus is a group of metabolic disorders characterized by high blood sugar levels.",
                "Pneumonia is an infection that inflames air sacs in one or both lungs.",
            ],
            "legal": [
                "A contract is a legally binding agreement between two or more parties.",
                "Due process refers to fair treatment through the normal judicial system.",
                "Tort law deals with civil wrongs that cause harm or loss to individuals.",
            ],
            "code": [
                "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
                "class Node: def __init__(self, data): self.data = data; self.next = None",
                "import pandas as pd; df = pd.read_csv('data.csv'); print(df.head())",
            ]
        }
        
        # Get sample data for the field
        texts = sample_data.get(self.specialization_field, sample_data["medical"])
        
        # Format for training
        formatted_texts = []
        for text in texts:
            formatted_text = f"<start_of_turn>user\nExplain this {self.specialization_field} concept.<end_of_turn>\n<start_of_turn>model\n{text}<end_of_turn>"
            formatted_texts.append(formatted_text)
        
        # Create dataset
        dataset = Dataset.from_dict({"text": formatted_texts})
        
        logger.info(f"✅ Created custom dataset with {len(dataset)} examples")
        return dataset
    
    def setup_training_arguments(self):
        """Setup training arguments optimized for 4GB GPU"""
        
        return TrainingArguments(
            output_dir=self.output_dir,
            overwrite_output_dir=True,
            
            # Training parameters
            num_train_epochs=3,
            per_device_train_batch_size=1,  # Small batch size for 4GB GPU
            gradient_accumulation_steps=8,  # Simulate larger batch size
            
            # Optimization
            learning_rate=2e-4,
            weight_decay=0.01,
            warmup_steps=100,
            
            # Memory optimization
            dataloader_pin_memory=False,
            gradient_checkpointing=True,
            fp16=True,  # Use mixed precision
            
            # Logging and saving
            logging_steps=10,
            save_steps=100,
            save_total_limit=2,
            
            # Evaluation
            evaluation_strategy="steps",
            eval_steps=50,
            
            # Other
            remove_unused_columns=False,
            report_to=None,  # Disable wandb/tensorboard for simplicity
        )
    
    def tokenize_function(self, examples):
        """Tokenize the dataset"""
        return self.tokenizer(
            examples["text"],
            truncation=True,
            padding=True,
            max_length=512,  # Reduced for memory efficiency
            return_tensors="pt"
        )
    
    def start_training(self):
        """Start the training process"""
        
        logger.info("🎯 Starting training process...")
        
        # Load model and tokenizer
        self.load_model_and_tokenizer()
        
        # Create dataset
        dataset = self.create_specialized_dataset()
        
        # Split dataset
        train_size = int(0.8 * len(dataset))
        train_dataset = dataset.select(range(train_size))
        eval_dataset = dataset.select(range(train_size, len(dataset)))
        
        # Tokenize datasets
        logger.info("🔤 Tokenizing datasets...")
        train_dataset = train_dataset.map(self.tokenize_function, batched=True)
        eval_dataset = eval_dataset.map(self.tokenize_function, batched=True)
        
        # Data collator
        data_collator = DataCollatorForLanguageModeling(
            tokenizer=self.tokenizer,
            mlm=False,  # Causal LM, not masked LM
        )
        
        # Training arguments
        training_args = self.setup_training_arguments()
        
        # Create trainer
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=eval_dataset,
            data_collator=data_collator,
            tokenizer=self.tokenizer,
        )
        
        # Start training
        logger.info("🚀 Starting training...")
        trainer.train()
        
        # Save the model
        logger.info("💾 Saving trained model...")
        trainer.save_model()
        self.tokenizer.save_pretrained(self.output_dir)
        
        logger.info("✅ Training completed successfully!")
        
        return trainer

def main():
    """Main function to run training"""
    
    print("🎯 Advanced Model Training Setup")
    print("=" * 50)
    
    # Available specialization fields
    fields = ["medical", "legal", "code", "science", "finance"]
    
    print("Available specialization fields:")
    for i, field in enumerate(fields, 1):
        print(f"{i}. {field.title()}")
    
    # Get user choice
    try:
        choice = input(f"\nChoose specialization field (1-{len(fields)}): ").strip()
        field_idx = int(choice) - 1
        
        if 0 <= field_idx < len(fields):
            chosen_field = fields[field_idx]
        else:
            print("Invalid choice, defaulting to medical")
            chosen_field = "medical"
            
    except ValueError:
        print("Invalid input, defaulting to medical")
        chosen_field = "medical"
    
    print(f"\n🎯 Selected field: {chosen_field.title()}")
    
    # Initialize trainer
    trainer = AdvancedModelTrainer(
        specialization_field=chosen_field,
        output_dir=f"./gemma-3n-{chosen_field}-finetuned"
    )
    
    # Start training
    try:
        trainer.start_training()
        print(f"\n🎉 Training completed! Model saved to ./gemma-3n-{chosen_field}-finetuned")
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        print("💡 Try reducing batch size or using CPU-only mode")

if __name__ == "__main__":
    main()
