#!/usr/bin/env python3
"""
Test vLLM with models that are compatible with your hardware
These models should work with vLLM and fit in 4GB VRAM
"""

import subprocess
import time
import requests
import json

def test_vllm_model(model_name, max_model_len=2048, gpu_memory_utilization=0.8):
    """Test a model with vLLM"""
    
    print(f"\n🧪 Testing {model_name} with vLLM")
    print("=" * 50)
    
    # Build vLLM command
    cmd = [
        "vllm", "serve", model_name,
        "--max-model-len", str(max_model_len),
        "--gpu-memory-utilization", str(gpu_memory_utilization),
        "--port", "8000"
    ]
    
    print(f"🚀 Starting vLLM server...")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        # Start vLLM server
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for server to start
        print("⏳ Waiting for server to start (30 seconds)...")
        time.sleep(30)
        
        # Check if server is running
        try:
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ vLLM server started successfully!")
                
                # Test inference
                test_inference()
                
            else:
                print(f"❌ Server health check failed: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Could not connect to server: {e}")
        
        # Stop the server
        print("🛑 Stopping vLLM server...")
        process.terminate()
        process.wait(timeout=10)
        
    except Exception as e:
        print(f"❌ Error running vLLM: {e}")

def test_inference():
    """Test inference with the running vLLM server"""
    
    print("\n🔬 Testing inference...")
    
    # Test data
    test_prompt = "Hello, how are you today?"
    
    # API request
    data = {
        "model": "current_model",
        "prompt": test_prompt,
        "max_tokens": 50,
        "temperature": 0.7
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/v1/completions",
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            generated_text = result['choices'][0]['text']
            print(f"✅ Inference successful!")
            print(f"📝 Input: {test_prompt}")
            print(f"🤖 Output: {generated_text}")
        else:
            print(f"❌ Inference failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Inference error: {e}")

def main():
    """Test multiple compatible models"""
    
    print("🎯 Testing vLLM-compatible models for 4GB GPU")
    print("=" * 60)
    
    # Models that should work with vLLM and fit in 4GB
    compatible_models = [
        {
            "name": "microsoft/Phi-3.5-mini-instruct",
            "description": "Microsoft Phi-3.5 Mini (3.8B parameters)",
            "max_len": 4096,
            "gpu_util": 0.9
        },
        {
            "name": "Qwen/Qwen2.5-3B-Instruct", 
            "description": "Qwen2.5 3B Instruct",
            "max_len": 2048,
            "gpu_util": 0.8
        },
        {
            "name": "google/gemma-2-2b-it",
            "description": "Google Gemma 2 2B Instruct",
            "max_len": 2048,
            "gpu_util": 0.8
        }
    ]
    
    print("Available models to test:")
    for i, model in enumerate(compatible_models, 1):
        print(f"{i}. {model['description']}")
        print(f"   Model: {model['name']}")
    
    print("\n" + "⚠️ " * 20)
    print("WARNING: Each test will:")
    print("- Download the model (several GB)")
    print("- Start a vLLM server")
    print("- Test inference")
    print("- Stop the server")
    print("This process takes 5-10 minutes per model")
    print("⚠️ " * 20)
    
    # Ask user which model to test
    try:
        choice = input(f"\nWhich model to test? (1-{len(compatible_models)}, or 'all'): ").strip()
        
        if choice.lower() == 'all':
            models_to_test = compatible_models
        else:
            idx = int(choice) - 1
            if 0 <= idx < len(compatible_models):
                models_to_test = [compatible_models[idx]]
            else:
                print("❌ Invalid choice")
                return
        
        # Test selected models
        for model in models_to_test:
            print(f"\n{'='*60}")
            print(f"Testing: {model['description']}")
            print(f"{'='*60}")
            
            test_vllm_model(
                model['name'],
                model['max_len'],
                model['gpu_util']
            )
            
            # Wait between tests
            if len(models_to_test) > 1:
                print("\n⏳ Waiting 10 seconds before next test...")
                time.sleep(10)
        
        print(f"\n✅ Testing complete!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Testing interrupted by user")
    except ValueError:
        print("❌ Invalid input")

if __name__ == "__main__":
    main()
