#!/usr/bin/env python3
"""
Run Hunyuan-A13B-Instruct with 4-bit quantization to fit in 4GB VRAM
This is the most compatible approach for your hardware
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
import gc
import time

def create_quantization_config():
    """Create 4-bit quantization configuration"""
    return BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_use_double_quant=True,
    )

def load_quantized_model(cache_dir="./hunyuan_cache"):
    """Load the model with 4-bit quantization"""
    
    model_name = "tencent/Hunyuan-A13B-Instruct"
    
    print("🔧 Setting up 4-bit quantization...")
    quantization_config = create_quantization_config()
    
    print("📥 Loading tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        cache_dir=cache_dir,
        trust_remote_code=True
    )
    
    print("🔄 Loading model with 4-bit quantization (this may take a while)...")
    print("⚠️  Note: First run will download ~26GB of model weights")
    
    try:
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            quantization_config=quantization_config,
            device_map="auto",
            trust_remote_code=True,
            torch_dtype=torch.float16,
            cache_dir=cache_dir,
            low_cpu_mem_usage=True
        )
        
        print("✅ Model loaded successfully with 4-bit quantization!")
        
        # Check memory usage
        if torch.cuda.is_available():
            memory_used = torch.cuda.memory_allocated() / 1024**3
            memory_total = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"📊 GPU Memory: {memory_used:.1f}GB / {memory_total:.1f}GB used")
        
        return model, tokenizer
        
    except Exception as e:
        print(f"❌ Error loading quantized model: {e}")
        return None, None

def chat_with_model(model, tokenizer, max_turns=5):
    """Interactive chat with the model"""
    
    print("\n🤖 Starting chat with Hunyuan-A13B-Instruct")
    print("💡 Type 'quit' to exit, 'clear' to clear conversation")
    print("=" * 50)
    
    conversation_history = []
    
    for turn in range(max_turns):
        try:
            # Get user input
            user_input = input(f"\n👤 You: ").strip()
            
            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'clear':
                conversation_history = []
                print("🗑️  Conversation cleared")
                continue
            elif not user_input:
                continue
            
            # Build conversation context
            conversation_history.append(f"User: {user_input}")
            
            # Create prompt (adjust format based on model's expected format)
            if len(conversation_history) == 1:
                prompt = user_input
            else:
                prompt = "\n".join(conversation_history[-6:])  # Keep last 3 exchanges
            
            print(f"🤖 Assistant: ", end="", flush=True)
            
            # Tokenize input
            inputs = tokenizer(prompt, return_tensors="pt", truncate=True, max_length=2048)
            
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # Generate response
            start_time = time.time()
            
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=150,
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                    pad_token_id=tokenizer.eos_token_id,
                    repetition_penalty=1.1
                )
            
            # Decode response
            response = tokenizer.decode(outputs[0][inputs['input_ids'].shape[1]:], skip_special_tokens=True)
            
            generation_time = time.time() - start_time
            tokens_generated = outputs[0].shape[1] - inputs['input_ids'].shape[1]
            tokens_per_second = tokens_generated / generation_time if generation_time > 0 else 0
            
            print(response)
            print(f"⏱️  ({tokens_generated} tokens, {tokens_per_second:.1f} tok/s)")
            
            conversation_history.append(f"Assistant: {response}")
            
        except KeyboardInterrupt:
            print("\n⚠️  Generation interrupted")
            break
        except Exception as e:
            print(f"\n❌ Error during generation: {e}")
            break
    
    print("\n👋 Chat ended")

def main():
    """Main function"""
    
    print("🚀 Hunyuan-A13B-Instruct with 4-bit Quantization")
    print("=" * 60)
    
    # Check system
    if not torch.cuda.is_available():
        print("⚠️  No CUDA GPU detected. This will be very slow on CPU.")
        response = input("Continue anyway? (y/n): ")
        if response.lower() != 'y':
            return
    
    # Load model
    model, tokenizer = load_quantized_model()
    
    if model is None or tokenizer is None:
        print("❌ Failed to load model. Exiting.")
        return
    
    # Start chat
    try:
        chat_with_model(model, tokenizer)
    finally:
        # Cleanup
        print("\n🧹 Cleaning up memory...")
        if 'model' in locals():
            del model
        if 'tokenizer' in locals():
            del tokenizer
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        print("✅ Cleanup complete")

if __name__ == "__main__":
    main()
